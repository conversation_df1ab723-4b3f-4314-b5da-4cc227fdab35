<?php
session_start();
require 'db.php';

// 檢查是否為POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit;
}

// 接收表單資料
$name = trim($_POST['name'] ?? '');
$phone = trim($_POST['phone'] ?? '');
$email = trim($_POST['email'] ?? '');
$content = trim($_POST['content'] ?? '');

$errors = [];

// 驗證必填欄位
if (empty($name)) {
    $errors[] = '姓名為必填欄位';
}

if (empty($phone)) {
    $errors[] = '電話為必填欄位';
} elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
    $errors[] = '電話格式錯誤，請輸入10位數字';
}

if (empty($email)) {
    $errors[] = '信箱為必填欄位';
} elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = '信箱格式錯誤';
}

if (empty($content)) {
    $errors[] = '內容為必填欄位';
}

// 如果有錯誤，返回錯誤訊息
if (!empty($errors)) {
    $_SESSION['errors'] = $errors;
    $_SESSION['old_input'] = $_POST;
    header('Location: index.php');
    exit;
}

// 新增留言到資料庫
try {
    $stmt = $pdo->prepare("INSERT INTO comment_list (name, phone, email, content, date) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([$name, $phone, $email, $content]);
    
    $_SESSION['success'] = '留言新增成功！';
} catch(PDOException $e) {
    $_SESSION['errors'] = ['資料庫錯誤：' . $e->getMessage()];
}

// 重導向回首頁
header('Location: index.php');
exit;
?>
