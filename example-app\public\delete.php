<?php
session_start();
require 'db.php';

// 檢查是否為管理者
if (!isset($_SESSION['admin']) || $_SESSION['admin'] !== true) {
    header('Location: index.php');
    exit;
}

// 檢查是否有提供ID
$id = $_GET['id'] ?? '';
if (empty($id) || !is_numeric($id)) {
    $_SESSION['errors'] = ['無效的留言ID'];
    header('Location: index.php');
    exit;
}

// 刪除留言
try {
    $stmt = $pdo->prepare("DELETE FROM comment_list WHERE id = ?");
    $result = $stmt->execute([$id]);
    
    if ($stmt->rowCount() > 0) {
        $_SESSION['success'] = '留言刪除成功！';
    } else {
        $_SESSION['errors'] = ['找不到指定的留言'];
    }
} catch(PDOException $e) {
    $_SESSION['errors'] = ['刪除失敗：' . $e->getMessage()];
}

// 重導向回首頁
header('Location: index.php');
exit;
?>
