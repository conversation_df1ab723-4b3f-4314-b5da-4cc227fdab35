@import url("https://fonts.googleapis.com/css?family=Noto+Sans+TC:100,400,700&display=swap&subset=chinese-traditional");
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

html, body {
  padding: 0;
  margin: 0; }

body {
  font-family: 'Noto Sans TC', sans-serif;
  background-color: #EEE;
  letter-spacing: 1px;
  line-height: 170%;
  color: #000; }

.wrap {
  width: 600px;
  margin: 0 auto; }

.titleArea {
  margin-top: 40px;
  margin-bottom: 20px; }

.fromArea {
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 40px;
  background-color: #fff;
  border-radius: 3px;
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); }
  .fromArea .row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 10px; }
    .fromArea .row label {
      width: 20%;
      text-align: center; }
      .fromArea .row label span {
        color: #F00; }
    .fromArea .row input[type="text"], .fromArea .row textarea {
      width: 80%;
      padding: 5px;
      font-family: inherit;
      font-size: inherit;
      border: 1px solid #CCC; }
  .fromArea .func {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    margin-bottom: 0; }
    .fromArea .func input[type="submit"] {
      display: inline-block;
      padding: 3px 25px;
      background: #F00;
      color: #FFF;
      border: 0;
      font-family: inherit;
      font-size: inherit;
      cursor: pointer;
      border-radius: 30px; }

.commentArea {
  margin-bottom: 100px; }
  .commentArea .item {
    padding-bottom: 20px;
    margin-bottom: 20px; }
    .commentArea .item:hover .delete {
      background-color: #F00 !important; }
    .commentArea .item:last-child {
      border-bottom: 0; }
    .commentArea .item .meta {
      position: relative;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center; }
      .commentArea .item .meta .image {
        width: 40px;
        height: 40px;
        margin-right: 10px;
        background-color: #CCC;
        border-radius: 50%; }
      .commentArea .item .meta .name {
        margin-right: 20px; }
        .commentArea .item .meta .name a {
          color: #000; }
      .commentArea .item .meta .phone {
        color: #AAA;
        margin-right: 20px; }
      .commentArea .item .meta .date {
        color: #AAA; }
      .commentArea .item .meta .delete {
        display: inline-block;
        position: absolute;
        top: 5px;
        right: 5px;
        padding: 5px;
        font-size: 13px;
        background: #CCC;
        color: #FFF;
        line-height: 1;
        cursor: pointer; }
    .commentArea .item .content {
      margin-left: 50px;
      margin-right: 20px;
      padding: 20px;
      background-color: #F9F9F9; }
