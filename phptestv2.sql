-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.4
-- https://www.phpmyadmin.net/
--
-- 主機: 127.0.0.1:3306
-- 產生時間： 
-- 伺服器版本: 5.7.24
-- PHP 版本： 7.0.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 資料庫： `phptestv2`
--

-- --------------------------------------------------------

--
-- 資料表結構 `comment_list`
--

DROP TABLE IF EXISTS `comment_list`;
CREATE TABLE IF NOT EXISTS `comment_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '電話',
  `email` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '信箱',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '留言內容',
  `date` datetime DEFAULT NULL COMMENT '時間',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 資料表的匯出資料 `comment_list`
--

INSERT INTO `comment_list` (`id`, `name`, `phone`, `email`, `content`, `date`) VALUES
(1, '黃阿凱', '0987654321', '<EMAIL>', '好熱喔...\r\n今天來喝可不可如何?', '2019-12-26 11:21:03'),
(2, '陳興興', '0985212365', '<EMAIL>', '好啊誰開單? 補個血，GoGoGo', '2019-12-26 11:23:45'),
(3, '衡老闆', '0969858742', '<EMAIL>', '有人可以幫忙開樓下門嗎\r\n有人可以幫忙開樓下門嗎\r\n有人可以幫忙開樓下門嗎', '2019-12-26 11:24:44'),
(4, '黃阿凱', '0987654321', '<EMAIL>', '哈囉各位夥伴，下午點心點一波\r\n選項：\r\nA　好4花生波羅包\r\nB 　冰火波羅包\r\nC　冰火波羅包+起司\r\nD   瀑布拉絲菠蘿包 \r\nE    海苔肉鬆菠蘿包 ', '2019-12-26 11:31:41'),
(5, '王威', '987456321', '<EMAIL>', 'Hi, 歡迎來到愛貝斯', '2019-12-26 11:43:11');
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
