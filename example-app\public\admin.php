<?php
session_start();

// 如果已經是管理者，重導向到首頁
if (isset($_SESSION['admin']) && $_SESSION['admin'] === true) {
    header('Location: index.php');
    exit;
}

// 取得錯誤訊息
$error = $_SESSION['login_error'] ?? '';
unset($_SESSION['login_error']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>管理者登入 - 愛貝斯 上班閒聊版</title>
    <link rel="stylesheet" href="css/master.css">
</head>
<body>
    <div class="wrap">
        <div class="titleArea">
            <h1>管理者登入</h1>
            <p style="text-align: right;"><a href="index.php" style="color: #000;">回到首頁</a></p>
        </div>
    </div>
    
    <div class="wrap">
        <div class="fromArea">
            <?php if (!empty($error)): ?>
                <div style="background-color: #ffebee; border: 1px solid #f44336; padding: 10px; margin-bottom: 20px; border-radius: 3px; color: #f44336; text-align: center;">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form action="login.php" method="post">
                <div class="row">
                    <label><span>*</span>帳號</label>
                    <input type="text" name="username" required>
                </div>
                <div class="row">
                    <label><span>*</span>密碼</label>
                    <input type="password" name="password" required>
                </div>
                <div class="row func">
                    <input type="submit" value="登入">
                </div>
            </form>
        </div>
    </div>
</body>
</html>
