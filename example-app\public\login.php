<?php
session_start();

// 檢查是否為POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: admin.php');
    exit;
}

// 接收表單資料
$username = trim($_POST['username'] ?? '');
$password = trim($_POST['password'] ?? '');

// 驗證帳號密碼
if ($username === 'admin' && $password === '0000') {
    // 登入成功，設定session
    $_SESSION['admin'] = true;
    header('Location: index.php');
    exit;
} else {
    // 登入失敗
    $_SESSION['login_error'] = '帳號或密碼錯誤';
    header('Location: admin.php');
    exit;
}
?>
