<?php
session_start();
require 'db.php';

// 從資料庫讀取所有留言，按日期新到舊排序
try {
    $stmt = $pdo->prepare("SELECT id, name, phone, email, content, date FROM comment_list ORDER BY date DESC");
    $stmt->execute();
    $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $comments = [];
    $error_message = "讀取留言失敗: " . $e->getMessage();
}

// 檢查是否為管理者
$is_admin = isset($_SESSION['admin']) && $_SESSION['admin'] === true;

// 取得錯誤訊息和成功訊息
$errors = $_SESSION['errors'] ?? [];
$success = $_SESSION['success'] ?? '';
$old_input = $_SESSION['old_input'] ?? [];

// 清除session中的訊息
unset($_SESSION['errors'], $_SESSION['success'], $_SESSION['old_input']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>愛貝斯 上班閒聊版</title>
    <link rel="stylesheet" href="css/master.css">
</head>
<body>

    <div class="wrap">
        <div class="titleArea">
            <h1>愛貝斯 上班閒聊版</h1>
            <?php if ($is_admin): ?>
                <p style="text-align: right; color: #F00;">管理者已登入 | <a href="logout.php" style="color: #F00;">登出</a></p>
            <?php else: ?>
                <p style="text-align: right;"><a href="admin.php" style="color: #000;">管理者登入</a></p>
            <?php endif; ?>
        </div>
    </div>
    <div class="wrap">
        <div class="fromArea">
            <!-- 顯示錯誤訊息 -->
            <?php if (!empty($errors)): ?>
                <div style="background-color: #ffebee; border: 1px solid #f44336; padding: 10px; margin-bottom: 20px; border-radius: 3px;">
                    <ul style="margin: 0; padding-left: 20px; color: #f44336;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- 顯示成功訊息 -->
            <?php if (!empty($success)): ?>
                <div style="background-color: #e8f5e8; border: 1px solid #4caf50; padding: 10px; margin-bottom: 20px; border-radius: 3px; color: #4caf50;">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- 留言板表單，記得驗證必填 -->
            <form action="save.php" method="post">
                <div class="row">
                    <label><span>*</span>姓名</label>
                    <input type="text" name="name" value="<?php echo htmlspecialchars($old_input['name'] ?? ''); ?>">
                </div>
                <div class="row">
                    <label><span>*</span>電話</label>
                    <input type="text" name="phone" value="<?php echo htmlspecialchars($old_input['phone'] ?? ''); ?>" placeholder="請輸入10位數字">
                </div>
                <div class="row">
                    <label><span>*</span>信箱</label>
                    <input type="text" name="email" value="<?php echo htmlspecialchars($old_input['email'] ?? ''); ?>">
                </div>
                <div class="row">
                    <label><span>*</span>內容</label>
                    <textarea name="content" placeholder="留言內容..."><?php echo htmlspecialchars($old_input['content'] ?? ''); ?></textarea>
                </div>
                <div class="row func">
                    <input type="submit" value="送出">
                </div>
            </form>
        </div>
    </div>
    <div class="wrap">
        <!-- 顯示所有訊息，並由新到舊排列 -->
        <div class="commentArea">
            <?php if (isset($error_message)): ?>
                <div style="color: red; text-align: center; padding: 20px;">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (empty($comments)): ?>
                <div style="text-align: center; padding: 40px; color: #AAA;">
                    目前沒有留言
                </div>
            <?php else: ?>
                <?php foreach ($comments as $comment): ?>
                    <div class="item">
                        <div class="meta">
                            <div class="image"></div>
                            <div class="name"><?php echo htmlspecialchars($comment['name']); ?></div>
                            <div class="phone"><?php echo htmlspecialchars($comment['phone']); ?></div>
                            <div class="date"><?php echo htmlspecialchars($comment['date']); ?></div>
                            <?php if ($is_admin): ?>
                                <div class="delete" onclick="deleteComment(<?php echo $comment['id']; ?>)">刪除</div>
                            <?php endif; ?>
                        </div>
                        <div class="content">
                            <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
<script>
function deleteComment(id) {
    if (confirm('確定要刪除這則留言嗎？')) {
        window.location.href = 'delete.php?id=' + id;
    }
}
</script>
</body>
</html>